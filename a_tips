因为内存不足，本文件夹原本是挂载在/下的，但是将数据迁移到了/home/<USER>/tyj/dataset/wyx/中
现在已经创建了两个软连接
第一/home/<USER>/wyx/dfq-toolkit/tools  ->/home/<USER>/tyj/dataset/wyx/zqa_is_raw/tools
第二/home/<USER>/wyx/dfq-toolkit/resnet/coco2017   ->/home/<USER>/tyj/dataset/wyx/coco2017  
下载资料的时候一定要下载到前者，不能下到后者，否则会直接使得软连接失效
第一轮：
# Stage I: 数据蒸馏
python -m distill_data \
    --data-path ./coco2017 \
    --output-dir ./stage1_output \
    --device cuda:0 \
    -b 4 \
    --calibration_size 100 \
    --iterations 200 \
    --do_clip

# Stage II: QAT训练（无知识蒸馏）
python -m feature_qat \
    --device cuda:0 \
    --data-path ./coco2017 \
    --output-dir ./stage2_output \
    --epochs 100 \
    --batch-size 4 \
    --pretrain maskrcnn_resnet18_fpn_coco.pth \
    --ckpt_path ./stage1_output/weights/iter100 \
    --train_kind generate \
    --mode quantize \
    --num_bits 8 \
    --calibration_size 100

# Stage III: 标准训练对比
python -m train \
    --device cuda:0 \
    --data-path ./coco2017 \
    --output-dir ./stage3_output \
    --epochs 100 \
    -b 4 \
    --pretrain maskrcnn_resnet18_fpn_coco.pth \
    --mode quantize \
    --num_bits 8

    第二轮：
    python -m distill_data \
    --data-path ./coco2017 \
    --output-dir ./stage1_output \
    --device cuda:0 \
    -b 8 \
    --calibration_size 200 \
    --iterations 300 \
    --do_clip

    python -m feature_qat \
    --device cuda:0 \
    --data-path ./coco2017 \
    --output-dir ./stage2_output \
    --epochs 150 \
    --batch-size 8 \
    --pretrain maskrcnn_resnet18_fpn_coco.pth \
    --ckpt_path ./stage1_output/weights/iter300 \
    --train_kind generate \
    --mode quantize \
    --num_bits 8 \
    --calibration_size 200 \

    python -m train \
    --device cuda:1 \
    --data-path ./coco2017 \
    --output-dir ./stage3_output \
    --epochs 150 \
    -b 8 \
    --pretrain maskrcnn_resnet18_fpn_coco.pth \
    --mode quantize \
    --num_bits 8 

    修正后：现在可以跑了，注意stage2andstage3batch-/_size写法不一样的......无语
    # Terminal 1 (Stage2)
python -m feature_qat --device cuda:0 --data-path ./coco2017 --output-dir ./stage2_output --epochs 5 --batch-size 8 --pretrain maskrcnn_resnet18_fpn_coco.pth --ckpt_path ./stage1_output/weights/iter300 --train_kind generate --mode quantize --num_bits 8 --calibration_size 200 

# Terminal 2 (Stage3)  
python -m train --device cuda:0 --data-path ./coco2017 --output-dir ./stage3_output --epochs 100 --batch_size 8 --pretrain maskrcnn_resnet18_fpn_coco.pth --mode quantize --num_bits 8


? 核心训练文件
train.py ? - 主要训练脚本 (Mask R-CNN + ResNet18)
feature_qat.py ? - DFQ量化感知训练脚本 (Stage II)
distill_data.py ? - 数据蒸馏脚本 (Stage I)
? 验证测试文件
validation.py ? - 保留 (支持ResNet18 + 量化模式的验证脚本)
? 多GPU训练文件
train_multi_GPU.py ? - 多GPU训练 (Mask R-CNN)
? 预测推理文件
predict.py ? - 保留 (ResNet18版本的预测脚本)
? 工具文件
plot_curve.py ? - 绘制训练曲线
draw_box_utils.py ? - 绘制检测框工具
transforms.py ? - 数据变换
my_dataset_coco.py ? - COCO数据集加载
my_dataset_voc.py ? - VOC数据集加载
? 新增的分析文件
compare_results.py ? - 结果对比分析脚本
auto_test.py ? - 自动化测试脚本  python auto_test.py --device cuda:0


