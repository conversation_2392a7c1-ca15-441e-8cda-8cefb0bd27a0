#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <c10/util/Optional.h>



#include <ATen/ops/symeig_ops.h>

namespace at {


// aten::symeig.e(Tensor self, bool eigenvectors=False, bool upper=True, *, Tensor(a!) e, Tensor(b!) V) -> (Tensor(a!) eigenvalues, Tensor(b!) eigenvectors)
inline ::std::tuple<at::Tensor &,at::Tensor &> symeig_out(at::Tensor & e, at::Tensor & V, const at::Tensor & self, bool eigenvectors=false, bool upper=true) {
    return at::_ops::symeig_e::call(self, eigenvectors, upper, e, V);
}

// aten::symeig.e(Tensor self, bool eigenvectors=False, bool upper=True, *, Tensor(a!) e, Tensor(b!) V) -> (Tensor(a!) eigenvalues, Tensor(b!) eigenvectors)
inline ::std::tuple<at::Tensor &,at::Tensor &> symeig_outf(const at::Tensor & self, bool eigenvectors, bool upper, at::Tensor & e, at::Tensor & V) {
    return at::_ops::symeig_e::call(self, eigenvectors, upper, e, V);
}

// aten::symeig(Tensor self, bool eigenvectors=False, bool upper=True) -> (Tensor eigenvalues, Tensor eigenvectors)
inline ::std::tuple<at::Tensor,at::Tensor> symeig(const at::Tensor & self, bool eigenvectors=false, bool upper=true) {
    return at::_ops::symeig::call(self, eigenvectors, upper);
}

}
