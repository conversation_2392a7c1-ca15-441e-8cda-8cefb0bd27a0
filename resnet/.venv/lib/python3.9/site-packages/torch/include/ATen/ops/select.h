#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <c10/util/Optional.h>



#include <ATen/ops/select_ops.h>

namespace at {


// aten::select.Dimname(Tensor(a) self, Dimname dim, int index) -> Tensor(a)
inline at::Tensor select(const at::Tensor & self, at::Dimname dim, int64_t index) {
    return at::_ops::select_Dimname::call(self, dim, index);
}

// aten::select.int(Tensor(a) self, int dim, int index) -> Tensor(a)
inline at::Tensor select(const at::Tensor & self, int64_t dim, int64_t index) {
    return at::_ops::select_int::call(self, dim, index);
}

}
