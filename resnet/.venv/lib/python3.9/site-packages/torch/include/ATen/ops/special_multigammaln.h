#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <c10/util/Optional.h>



#include <ATen/ops/special_multigammaln_ops.h>

namespace at {


// aten::special_multigammaln(Tensor self, int p) -> Tensor
inline at::Tensor special_multigammaln(const at::Tensor & self, int64_t p) {
    return at::_ops::special_multigammaln::call(self, p);
}

// aten::special_multigammaln.out(Tensor self, int p, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & special_multigammaln_out(at::Tensor & out, const at::Tensor & self, int64_t p) {
    return at::_ops::special_multigammaln_out::call(self, p, out);
}

// aten::special_multigammaln.out(Tensor self, int p, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & special_multigammaln_outf(const at::Tensor & self, int64_t p, at::Tensor & out) {
    return at::_ops::special_multigammaln_out::call(self, p, out);
}

}
