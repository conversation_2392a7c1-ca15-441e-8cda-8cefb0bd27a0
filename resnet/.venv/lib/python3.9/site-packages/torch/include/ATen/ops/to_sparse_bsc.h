#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <c10/util/Optional.h>



#include <ATen/ops/to_sparse_bsc_ops.h>

namespace at {


// aten::to_sparse_bsc.out(Tensor self, int[2] blocksize, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & to_sparse_bsc_out(at::Tensor & out, const at::Tensor & self, at::IntArrayRef blocksize) {
    return at::_ops::to_sparse_bsc_out::call(self, blocksize, out);
}

// aten::to_sparse_bsc.out(Tensor self, int[2] blocksize, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & to_sparse_bsc_outf(const at::Tensor & self, at::IntArrayRef blocksize, at::Tensor & out) {
    return at::_ops::to_sparse_bsc_out::call(self, blocksize, out);
}

}
