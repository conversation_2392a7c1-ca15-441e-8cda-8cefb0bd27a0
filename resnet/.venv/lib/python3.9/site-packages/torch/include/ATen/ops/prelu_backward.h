#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <c10/util/Optional.h>



#include <ATen/ops/prelu_backward_ops.h>

namespace at {


// aten::prelu_backward(Tensor grad_output, Tensor self, Tensor weight) -> (Tensor, Tensor)
inline ::std::tuple<at::Tensor,at::Tensor> prelu_backward(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & weight) {
    return at::_ops::prelu_backward::call(grad_output, self, weight);
}

// aten::prelu_backward.out(Tensor grad_output, Tensor self, Tensor weight, *, Tensor(a!) out0, Tensor(b!) out1) -> (Tensor(a!), Tensor(b!))
inline ::std::tuple<at::Tensor &,at::Tensor &> prelu_backward_out(at::Tensor & out0, at::Tensor & out1, const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & weight) {
    return at::_ops::prelu_backward_out::call(grad_output, self, weight, out0, out1);
}

// aten::prelu_backward.out(Tensor grad_output, Tensor self, Tensor weight, *, Tensor(a!) out0, Tensor(b!) out1) -> (Tensor(a!), Tensor(b!))
inline ::std::tuple<at::Tensor &,at::Tensor &> prelu_backward_outf(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & weight, at::Tensor & out0, at::Tensor & out1) {
    return at::_ops::prelu_backward_out::call(grad_output, self, weight, out0, out1);
}

}
